/**
 * Lindo Enhanced UI JavaScript
 * Simple enhancements for Odoo UI components
 */

// Simple vanilla JavaScript approach for better compatibility
(function() {
    'use strict';

    // Wait for DOM to be ready
    function ready(fn) {
        if (document.readyState !== 'loading') {
            fn();
        } else {
            document.addEventListener('DOMContentLoaded', fn);
        }
    }

    // Enhanced navbar scroll effect
    function enhanceNavbar() {
        var navbar = document.querySelector('.o_main_navbar');
        if (!navbar) return;

        window.addEventListener('scroll', function() {
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Enhance dropdown animations
        var dropdowns = navbar.querySelectorAll('.dropdown');
        dropdowns.forEach(function(dropdown) {
            dropdown.addEventListener('show.bs.dropdown', function() {
                var menu = this.querySelector('.dropdown-menu');
                if (menu) {
                    menu.classList.add('fade-in');
                }
            });
        });
    }

    // Enhanced list views
    function enhanceListViews() {
        var listViews = document.querySelectorAll('.o_list_view');
        listViews.forEach(function(listView) {
            listView.classList.add('fade-in');

            var rows = listView.querySelectorAll('tbody tr');
            rows.forEach(function(row) {
                row.addEventListener('mouseenter', function() {
                    this.classList.add('slide-in-left');
                });
                row.addEventListener('mouseleave', function() {
                    this.classList.remove('slide-in-left');
                });
            });
        });
    }

    // Enhanced form views
    function enhanceFormViews() {
        var formViews = document.querySelectorAll('.o_form_view');
        formViews.forEach(function(formView) {
            formView.classList.add('fade-in');

            var fields = formView.querySelectorAll('.o_field_widget input, .o_field_widget select, .o_field_widget textarea');
            fields.forEach(function(field) {
                field.addEventListener('focus', function() {
                    var widget = this.closest('.o_field_widget');
                    if (widget) {
                        widget.classList.add('focused');
                    }
                });
                field.addEventListener('blur', function() {
                    var widget = this.closest('.o_field_widget');
                    if (widget) {
                        widget.classList.remove('focused');
                    }
                });
            });
        });
    }

    // Enhanced kanban views
    function enhanceKanbanViews() {
        var kanbanViews = document.querySelectorAll('.o_kanban_view');
        kanbanViews.forEach(function(kanbanView) {
            var records = kanbanView.querySelectorAll('.o_kanban_record');
            records.forEach(function(record, index) {
                setTimeout(function() {
                    record.classList.add('fade-in');
                }, index * 100);
            });
        });
    }

    // Enhanced calendar views
    function enhanceCalendarViews() {
        var calendarViews = document.querySelectorAll('.o_calendar_view');
        calendarViews.forEach(function(calendarView) {
            calendarView.classList.add('fade-in');

            // Enhance calendar events
            var events = calendarView.querySelectorAll('.fc-event');
            events.forEach(function(event) {
                event.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                });
                event.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    }

    // Enhanced pivot views
    function enhancePivotViews() {
        var pivotViews = document.querySelectorAll('.o_pivot_view');
        pivotViews.forEach(function(pivotView) {
            pivotView.classList.add('fade-in');

            var cells = pivotView.querySelectorAll('tbody td');
            cells.forEach(function(cell) {
                cell.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(102, 126, 234, 0.1)';
                });
                cell.addEventListener('mouseleave', function() {
                    this.style.background = '';
                });
            });
        });
    }

    // Enhanced graph views
    function enhanceGraphViews() {
        var graphViews = document.querySelectorAll('.o_graph_view');
        graphViews.forEach(function(graphView) {
            graphView.classList.add('fade-in');
        });
    }

    // Enhanced search views
    function enhanceSearchViews() {
        var searchViews = document.querySelectorAll('.o_searchview');
        searchViews.forEach(function(searchView) {
            var input = searchView.querySelector('.o_searchview_input');
            if (input) {
                input.addEventListener('focus', function() {
                    searchView.classList.add('focused');
                });
                input.addEventListener('blur', function() {
                    searchView.classList.remove('focused');
                });
            }
        });
    }

    // Enhanced activity views
    function enhanceActivityViews() {
        var activityViews = document.querySelectorAll('.o_activity_view');
        activityViews.forEach(function(activityView) {
            activityView.classList.add('fade-in');

            var records = activityView.querySelectorAll('.o_activity_record');
            records.forEach(function(record) {
                record.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateX(5px)';
                });
                record.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateX(0)';
                });
            });
        });
    }

    // Enhanced gantt views
    function enhanceGanttViews() {
        var ganttViews = document.querySelectorAll('.o_gantt_view');
        ganttViews.forEach(function(ganttView) {
            ganttView.classList.add('fade-in');

            var rows = ganttView.querySelectorAll('.o_gantt_row');
            rows.forEach(function(row) {
                row.addEventListener('mouseenter', function() {
                    this.style.background = 'rgba(102, 126, 234, 0.05)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.background = '';
                });
            });
        });
    }

    // Enhanced dashboard views
    function enhanceDashboardViews() {
        var dashboards = document.querySelectorAll('.o_dashboard');
        dashboards.forEach(function(dashboard) {
            var actions = dashboard.querySelectorAll('.o_dashboard_action');
            actions.forEach(function(action, index) {
                setTimeout(function() {
                    action.classList.add('fade-in');
                }, index * 150);

                action.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px)';
                });
                action.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    }

    // Enhanced tabs/notebook
    function enhanceTabs() {
        var tabs = document.querySelectorAll('.nav-tabs .nav-link');
        tabs.forEach(function(tab) {
            tab.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateY(-2px)';
                }
            });
            tab.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateY(0)';
                }
            });
        });
    }

    // Enhanced statusbar
    function enhanceStatusbar() {
        var statusItems = document.querySelectorAll('.o_statusbar_status');
        statusItems.forEach(function(item) {
            item.addEventListener('mouseenter', function() {
                if (!this.classList.contains('o_arrow_button_current')) {
                    this.style.transform = 'translateY(-1px)';
                }
            });
            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('o_arrow_button_current')) {
                    this.style.transform = 'translateY(0)';
                }
            });
        });
    }

    // Enhanced field widgets
    function enhanceFieldWidgets() {
        // Enhance all input fields
        var inputs = document.querySelectorAll('.o_field_widget input, .o_field_widget select, .o_field_widget textarea');
        inputs.forEach(function(input) {
            input.addEventListener('focus', function() {
                var widget = this.closest('.o_field_widget');
                if (widget) {
                    widget.classList.add('focused');
                }
            });
            input.addEventListener('blur', function() {
                var widget = this.closest('.o_field_widget');
                if (widget) {
                    widget.classList.remove('focused');
                }
            });
        });

        // Enhance priority stars
        var stars = document.querySelectorAll('.o_priority .o_priority_star');
        stars.forEach(function(star) {
            star.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.2)';
            });
            star.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        // Enhance binary/image fields
        var binaryFields = document.querySelectorAll('.o_field_binary, .o_field_image');
        binaryFields.forEach(function(field) {
            field.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.02)';
            });
            field.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });
    }

    // Enhanced buttons
    function enhanceButtons() {
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('btn')) {
                e.target.classList.add('clicked');
                setTimeout(function() {
                    e.target.classList.remove('clicked');
                }, 200);
            }
        });
    }

    // Enhanced modals
    function enhanceModals() {
        document.addEventListener('show.bs.modal', function(e) {
            var modal = e.target.querySelector('.modal-content');
            if (modal) {
                modal.classList.add('fade-in');
            }
        });
    }

    // Smooth scrolling for anchor links
    function setupSmoothScrolling() {
        document.addEventListener('click', function(e) {
            var target = e.target;
            if (target.tagName === 'A' && target.getAttribute('href') && target.getAttribute('href').startsWith('#')) {
                var targetElement = document.querySelector(target.getAttribute('href'));
                if (targetElement) {
                    e.preventDefault();
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }
        });
    }

    // Intersection Observer for fade-in animations
    function setupIntersectionObserver() {
        if ('IntersectionObserver' in window) {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('fade-in');
                    }
                });
            }, {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            });

            // Observe view controllers
            var viewControllers = document.querySelectorAll('.o_view_controller');
            viewControllers.forEach(function(controller) {
                observer.observe(controller);
            });
        }
    }

    // Re-enhance views when new content is loaded
    function setupMutationObserver() {
        if ('MutationObserver' in window) {
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            if (node.classList && node.classList.contains('o_view_controller')) {
                                setTimeout(function() {
                                    enhanceListViews();
                                    enhanceFormViews();
                                    enhanceKanbanViews();
                                }, 100);
                            }
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
    }

    // Enhanced Responsive System
    var ResponsiveSystem = {
        breakpoints: {
            xs: 575.98,
            sm: 767.98,
            md: 991.98,
            lg: 1199.98,
            xl: Infinity
        },

        currentBreakpoint: 'xl',

        init: function() {
            this.updateBreakpoint();
            this.setupResizeListener();
            this.setupTouchHandlers();
            this.setupMobileNavigation();
        },

        updateBreakpoint: function() {
            var width = window.innerWidth;
            var newBreakpoint = 'xl';

            if (width <= this.breakpoints.xs) {
                newBreakpoint = 'xs';
            } else if (width <= this.breakpoints.sm) {
                newBreakpoint = 'sm';
            } else if (width <= this.breakpoints.md) {
                newBreakpoint = 'md';
            } else if (width <= this.breakpoints.lg) {
                newBreakpoint = 'lg';
            }

            if (newBreakpoint !== this.currentBreakpoint) {
                this.currentBreakpoint = newBreakpoint;
                this.onBreakpointChange(newBreakpoint);
            }
        },

        onBreakpointChange: function(breakpoint) {
            document.body.className = document.body.className.replace(/\blindo-bp-\w+/g, '');
            document.body.classList.add('lindo-bp-' + breakpoint);

            // Adjust UI based on breakpoint
            this.adjustUIForBreakpoint(breakpoint);
        },

        adjustUIForBreakpoint: function(breakpoint) {
            if (breakpoint === 'xs' || breakpoint === 'sm') {
                this.enableMobileMode();
            } else {
                this.disableMobileMode();
            }
        },

        enableMobileMode: function() {
            document.body.classList.add('lindo-mobile-mode');
            this.addMobileTouchFeedback();
        },

        disableMobileMode: function() {
            document.body.classList.remove('lindo-mobile-mode');
        },

        setupResizeListener: function() {
            var self = this;
            var resizeTimeout;

            window.addEventListener('resize', function() {
                clearTimeout(resizeTimeout);
                resizeTimeout = setTimeout(function() {
                    self.updateBreakpoint();
                }, 250);
            });
        },

        setupTouchHandlers: function() {
            var self = this;

            if ('ontouchstart' in window) {
                document.addEventListener('touchstart', function(e) {
                    self.handleTouchStart(e);
                }, { passive: true });

                document.addEventListener('touchend', function(e) {
                    self.handleTouchEnd(e);
                }, { passive: true });
            }
        },

        handleTouchStart: function(e) {
            var target = e.target.closest('.btn, .o_list_table tbody tr, .o_kanban_record');
            if (target) {
                target.classList.add('lindo-touch-active');
            }
        },

        handleTouchEnd: function(e) {
            var target = e.target.closest('.btn, .o_list_table tbody tr, .o_kanban_record');
            if (target) {
                setTimeout(function() {
                    target.classList.remove('lindo-touch-active');
                }, 150);
            }
        },

        setupMobileNavigation: function() {
            var navbar = document.querySelector('.o_main_navbar');
            if (navbar && !navbar.querySelector('.lindo-mobile-toggle')) {
                var toggle = document.createElement('button');
                toggle.className = 'lindo-mobile-toggle';
                toggle.innerHTML = '☰';
                toggle.style.cssText = `
                    background: rgba(255, 255, 255, 0.1);
                    border: none;
                    border-radius: 50%;
                    width: 36px;
                    height: 36px;
                    color: white;
                    font-size: 16px;
                    cursor: pointer;
                    margin-left: 8px;
                    display: none;
                `;

                var self = this;
                toggle.onclick = function() {
                    self.toggleMobileMenu();
                };

                var systray = navbar.querySelector('.o_menu_systray');
                if (systray) {
                    systray.appendChild(toggle);
                }
            }
        },

        toggleMobileMenu: function() {
            var navbar = document.querySelector('.o_main_navbar');
            var sections = navbar.querySelector('.o_menu_sections');

            if (sections) {
                sections.classList.toggle('lindo-mobile-open');
            }
        },

        addMobileTouchFeedback: function() {
            if ('vibrate' in navigator) {
                document.addEventListener('click', function(e) {
                    var target = e.target.closest('.btn');
                    if (target) {
                        navigator.vibrate(10);
                    }
                });
            }
        },

        isMobile: function() {
            return this.currentBreakpoint === 'xs' || this.currentBreakpoint === 'sm';
        },

        isTablet: function() {
            return this.currentBreakpoint === 'md';
        },

        isDesktop: function() {
            return this.currentBreakpoint === 'lg' || this.currentBreakpoint === 'xl';
        }
    };

    // Initialize all enhancements
    function initializeEnhancements() {
        // Initialize responsive system
        ResponsiveSystem.init();

        // Initialize existing enhancements
        enhanceNavbar();
        enhanceListViews();
        enhanceFormViews();
        enhanceKanbanViews();
        enhanceCalendarViews();
        enhancePivotViews();
        enhanceGraphViews();
        enhanceSearchViews();
        enhanceActivityViews();
        enhanceGanttViews();
        enhanceDashboardViews();
        enhanceTabs();
        enhanceStatusbar();
        enhanceFieldWidgets();
        enhanceButtons();
        enhanceModals();
        setupSmoothScrolling();
        setupIntersectionObserver();
        setupMutationObserver();
    }

    // Start when DOM is ready
    ready(initializeEnhancements);

    // Re-initialize when page changes (for SPA behavior)
    window.addEventListener('popstate', function() {
        setTimeout(initializeEnhancements, 100);
    });

})();

// Odoo module definition (optional, for better integration)
if (typeof odoo !== 'undefined') {
    odoo.define('lindo.enhanced_ui', [], function() {
        'use strict';

        // The enhancements are already applied via vanilla JS above
        // This is just for Odoo module system compatibility

        return {
            name: 'Lindo Enhanced UI',
            version: '1.0.0'
        };
    });
}