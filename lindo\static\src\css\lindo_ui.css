/* Lindo Modern UI Styles - Enhanced Odoo Classes */

/* Enhanced Odoo Main Navbar */
.o_main_navbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.3s ease !important;
    border-bottom: none !important;
}

.o_main_navbar.scrolled {
    background: rgba(102, 126, 234, 0.95) !important;
    backdrop-filter: blur(10px);
}

/* Enhanced Navbar Brand */
.o_menu_brand {
    color: white !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.o_menu_brand:hover {
    color: #f0f0f0 !important;
    transform: translateY(-1px);
}

/* Enhanced Navbar Apps Menu */
.o_navbar_apps_menu .dropdown-toggle {
    background: rgba(255, 255, 255, 0.1) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    color: white !important;
}

.o_navbar_apps_menu .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced Menu Sections */
.o_menu_sections .o_nav_entry,
.o_menu_sections .dropdown-toggle {
    color: white !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
    margin: 0 5px !important;
}

.o_menu_sections .o_nav_entry:hover,
.o_menu_sections .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.15) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.o_menu_sections .dropdown.show > .dropdown-toggle {
    background: rgba(255, 255, 255, 0.25) !important;
    color: white !important;
}

/* Enhanced Systray */
.o_menu_systray .o_nav_entry,
.o_menu_systray .dropdown-toggle {
    color: white !important;
    border-radius: 50% !important;
    width: 40px !important;
    height: 40px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.3s ease !important;
}

.o_menu_systray .o_nav_entry:hover,
.o_menu_systray .dropdown-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1);
}

/* Enhanced Dropdown Menus */
.o_main_navbar .dropdown-menu {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    backdrop-filter: blur(10px);
    margin-top: 8px !important;
}

.o_main_navbar .dropdown-item {
    border-radius: 8px !important;
    margin: 2px 8px !important;
    transition: all 0.3s ease !important;
}

.o_main_navbar .dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    transform: translateX(5px);
}

/* Enhanced Odoo Web Client */
.o_web_client {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

/* Enhanced Action Manager */
.o_action_manager {
    background: transparent !important;
}

.o_action_manager > .o_view_controller {
    background: white !important;
    border-radius: 12px !important;
    margin: 20px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

/* Enhanced Control Panel */
.o_control_panel {
    background: white !important;
    border-radius: 12px 12px 0 0 !important;
    border-bottom: 1px solid #e9ecef !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
}

.o_control_panel .breadcrumb {
    background: transparent !important;
    margin: 0 !important;
    padding: 0 !important;
}

.o_control_panel .breadcrumb-item {
    font-weight: 500 !important;
}

.o_control_panel .breadcrumb-item.active {
    color: #667eea !important;
    font-weight: 600 !important;
}

/* Enhanced Search Panel */
.o_search_panel {
    background: white !important;
    border-radius: 12px !important;
    margin: 20px 0 20px 20px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.o_search_panel_section_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: 8px !important;
    margin: 10px !important;
    padding: 12px 16px !important;
}

.o_search_panel_category_value,
.o_search_panel_filter_value {
    border-radius: 6px !important;
    margin: 2px 10px !important;
    transition: all 0.3s ease !important;
}

.o_search_panel_category_value:hover,
.o_search_panel_filter_value:hover {
    background: #f8f9fa !important;
    transform: translateX(5px);
}

.o_search_panel_category_value.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* Main Content Area */
.lindo-main-content {
    margin-left: 280px;
    margin-top: 60px;
    padding: 30px;
    min-height: calc(100vh - 60px);
    background: #f8f9fa;
    transition: margin-left 0.3s ease;
}

.lindo-sidebar.collapsed + .lindo-main-content {
    margin-left: 70px;
}

/* Content Cards */
.lindo-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.lindo-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
}

.lindo-card-header {
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.lindo-card-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
}

.lindo-card-body {
    padding: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .lindo-navbar-nav {
        display: none;
    }

    .lindo-navbar-toggle {
        display: block;
    }

    .lindo-sidebar {
        transform: translateX(-100%);
    }

    .lindo-sidebar.mobile-open {
        transform: translateX(0);
    }

    .lindo-main-content {
        margin-left: 0;
        padding: 20px;
    }

    .lindo-sidebar.collapsed + .lindo-main-content {
        margin-left: 0;
    }
}

/* Animation utilities */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Custom scrollbar for sidebar */
.lindo-sidebar::-webkit-scrollbar {
    width: 6px;
}

.lindo-sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.lindo-sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.lindo-sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Button styles */
.lindo-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.lindo-btn-primary {
    background: #667eea;
    color: white;
}

.lindo-btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.lindo-btn-secondary {
    background: #6c757d;
    color: white;
}

.lindo-btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Form enhancements */
.lindo-form-group {
    margin-bottom: 20px;
}

.lindo-form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.lindo-form-control {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 5px;
    transition: border-color 0.3s ease;
}

.lindo-form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Status indicators */
.lindo-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.lindo-status-success {
    background: #d4edda;
    color: #155724;
}

.lindo-status-warning {
    background: #fff3cd;
    color: #856404;
}

.lindo-status-danger {
    background: #f8d7da;
    color: #721c24;
}

.lindo-status-info {
    background: #d1ecf1;
    color: #0c5460;
}
/* Enhanced Odoo List Views */
.o_list_view {
    background: white !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.o_list_table {
    border-radius: 12px !important;
    overflow: hidden !important;
}

.o_list_table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.o_list_table thead th {
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    letter-spacing: 1px !important;
}

.o_list_table tbody tr {
    transition: all 0.3s ease !important;
    border-bottom: 1px solid #f1f3f4 !important;
}

.o_list_table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
    transform: translateX(5px);
}

.o_list_table tbody td {
    padding: 16px 12px !important;
    border: none !important;
    vertical-align: middle !important;
}

/* Enhanced Odoo Form Views */
.o_form_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

.o_form_sheet_bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-radius: 12px !important;
}

.o_form_sheet {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
    margin: 20px !important;
    padding: 30px !important;
}

/* Enhanced Form Groups */
.o_group {
    margin-bottom: 24px !important;
}

.o_group .o_td_label {
    font-weight: 600 !important;
    color: #495057 !important;
    padding: 12px 16px 12px 0 !important;
}

.o_field_widget {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget input,
.o_field_widget select,
.o_field_widget textarea {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
}

.o_field_widget input:focus,
.o_field_widget select:focus,
.o_field_widget textarea:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

/* Enhanced Buttons */
.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
}

.btn-primary:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

.btn-secondary {
    background: #6c757d !important;
    border: none !important;
    border-radius: 8px !important;
    padding: 12px 24px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
}

.btn-secondary:hover {
    background: #5a6268 !important;
    transform: translateY(-2px) !important;
}

/* Enhanced Kanban Views */
.o_kanban_view {
    background: transparent !important;
}

.o_kanban_record {
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
    border: none !important;
    transition: all 0.3s ease !important;
    margin-bottom: 16px !important;
}

.o_kanban_record:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.o_kanban_record .o_kanban_record_title {
    font-weight: 600 !important;
    color: #495057 !important;
}

/* Enhanced Calendar Views */
.o_calendar_view {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.fc-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 12px 12px 0 0 !important;
}

.fc-toolbar .fc-button {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.fc-toolbar .fc-button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px);
}

/* Enhanced Pivot Views */
.o_pivot_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
}

.o_pivot table {
    border-radius: 12px !important;
    overflow: hidden !important;
}

.o_pivot table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
}

/* Enhanced Graph Views */
.o_graph_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 20px !important;
}

/* Enhanced Modal Dialogs */
.modal-content {
    border-radius: 16px !important;
    border: none !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2) !important;
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 16px 16px 0 0 !important;
    border-bottom: none !important;
    padding: 24px !important;
}

.modal-title {
    font-weight: 600 !important;
    font-size: 18px !important;
}

.modal-body {
    padding: 24px !important;
}

.modal-footer {
    border-top: 1px solid #e9ecef !important;
    padding: 20px 24px !important;
    border-radius: 0 0 16px 16px !important;
}

/* Enhanced Notifications */
.o_notification {
    border-radius: 12px !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    border: none !important;
    backdrop-filter: blur(10px) !important;
}

.o_notification.o_notification_success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
}

.o_notification.o_notification_warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
    color: white !important;
}

.o_notification.o_notification_danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%) !important;
    color: white !important;
}

/* Enhanced Status Bar */
.o_statusbar_status {
    border-radius: 8px !important;
    margin: 0 4px !important;
    transition: all 0.3s ease !important;
}

.o_statusbar_status.o_arrow_button_current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* Enhanced Chatter */
.o_chatter {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
}

.o_chatter_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-radius: 12px 12px 0 0 !important;
    padding: 20px !important;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .o_main_navbar {
        padding: 10px !important;
    }

    .o_action_manager > .o_view_controller {
        margin: 10px !important;
        border-radius: 8px !important;
    }

    .o_form_sheet {
        margin: 10px !important;
        padding: 20px !important;
    }

    .o_list_table thead th,
    .o_list_table tbody td {
        padding: 12px 8px !important;
        font-size: 13px !important;
    }
}

/* Animation utilities for enhanced UX */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

/* Enhanced Odoo UI - Complete */
.o_web_client .fade-in {
    animation: fadeInUp 0.6s ease-out;
}

.o_web_client .slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

/* Additional CSS classes for enhanced interactions */
.btn.clicked {
    transform: scale(0.95) !important;
    transition: transform 0.1s ease !important;
}

.o_field_widget.focused {
    transform: scale(1.02) !important;
    z-index: 10 !important;
}

.o_main_navbar.scrolled {
    background: rgba(102, 126, 234, 0.95) !important;
    backdrop-filter: blur(10px) !important;
}

/* Enhanced hover states */
.o_list_table tbody tr.slide-in-left {
    transform: translateX(5px) !important;
}

.o_kanban_record.fade-in {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

/* Smooth transitions for all enhanced elements */
.o_main_navbar,
.o_list_table tbody tr,
.o_field_widget,
.o_kanban_record,
.btn {
    transition: all 0.3s ease !important;
}
/* ========================================
   ENHANCED ODOO XML VIEWS - COMPREHENSIVE
   ======================================== */

/* Enhanced Tree View (.o_list_view) */
.o_list_view .o_list_table {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
}

.o_list_view .o_list_table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
    text-transform: uppercase !important;
    font-size: 12px !important;
    letter-spacing: 1px !important;
    position: relative !important;
}

.o_list_view .o_list_table thead th:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%) !important;
    transform: translateY(-1px) !important;
}

.o_list_view .o_list_table tbody tr {
    transition: all 0.3s ease !important;
    border-bottom: 1px solid #f1f3f4 !important;
    position: relative !important;
}

.o_list_view .o_list_table tbody tr:hover {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.08) 0%, rgba(118, 75, 162, 0.08) 100%) !important;
    transform: translateX(8px) !important;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15) !important;
}

.o_list_view .o_list_table tbody tr.o_selected_row {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%) !important;
    border-left: 4px solid #667eea !important;
}

.o_list_view .o_list_table tbody td {
    padding: 16px 12px !important;
    border: none !important;
    vertical-align: middle !important;
    position: relative !important;
}

/* Enhanced Form View (.o_form_view) */
.o_form_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
    margin: 20px !important;
}

.o_form_view .o_form_sheet_bg {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
    border-radius: 12px !important;
    padding: 0 !important;
}

.o_form_view .o_form_sheet {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
    margin: 20px !important;
    padding: 30px !important;
    position: relative !important;
}

.o_form_view .o_form_sheet::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 12px 12px 0 0 !important;
}

/* Enhanced Form Groups */
.o_form_view .o_group {
    margin-bottom: 24px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    border-radius: 8px !important;
    padding: 20px !important;
    border: 1px solid rgba(102, 126, 234, 0.1) !important;
}

.o_form_view .o_group .o_td_label {
    font-weight: 600 !important;
    color: #495057 !important;
    padding: 12px 16px 12px 0 !important;
    position: relative !important;
}

.o_form_view .o_group .o_td_label::after {
    content: '' !important;
    position: absolute !important;
    bottom: 0 !important;
    left: 0 !important;
    width: 30px !important;
    height: 2px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 1px !important;
}

/* Enhanced Field Widgets */
.o_form_view .o_field_widget {
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.o_form_view .o_field_widget input,
.o_form_view .o_field_widget select,
.o_form_view .o_field_widget textarea {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    background: white !important;
}

.o_form_view .o_field_widget input:focus,
.o_form_view .o_field_widget select:focus,
.o_form_view .o_field_widget textarea:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
    transform: translateY(-1px) !important;
}

.o_form_view .o_field_widget.o_field_highlight {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
    border-radius: 8px !important;
    padding: 8px !important;
}

/* Enhanced Kanban View (.o_kanban_view) */
.o_kanban_view {
    background: transparent !important;
    padding: 20px !important;
}

.o_kanban_view .o_kanban_record {
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08) !important;
    border: none !important;
    transition: all 0.3s ease !important;
    margin-bottom: 16px !important;
    background: white !important;
    position: relative !important;
    overflow: hidden !important;
}

.o_kanban_view .o_kanban_record::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.o_kanban_view .o_kanban_record:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.o_kanban_view .o_kanban_record .o_kanban_record_title {
    font-weight: 600 !important;
    color: #495057 !important;
    font-size: 16px !important;
    margin-bottom: 8px !important;
}

.o_kanban_view .o_kanban_record .o_kanban_record_subtitle {
    color: #6c757d !important;
    font-size: 14px !important;
}

.o_kanban_view .o_kanban_record .o_dropdown_kanban {
    background: rgba(102, 126, 234, 0.1) !important;
    border-radius: 50% !important;
    transition: all 0.3s ease !important;
}

.o_kanban_view .o_kanban_record .o_dropdown_kanban:hover {
    background: rgba(102, 126, 234, 0.2) !important;
    transform: scale(1.1) !important;
}

/* Enhanced Calendar View (.o_calendar_view) */
.o_calendar_view {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    background: white !important;
}

.o_calendar_view .fc-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    border-radius: 12px 12px 0 0 !important;
    border: none !important;
}

.o_calendar_view .fc-toolbar .fc-button {
    background: rgba(255, 255, 255, 0.2) !important;
    border: none !important;
    border-radius: 8px !important;
    color: white !important;
    transition: all 0.3s ease !important;
    padding: 8px 16px !important;
}

.o_calendar_view .fc-toolbar .fc-button:hover {
    background: rgba(255, 255, 255, 0.3) !important;
    transform: translateY(-2px) !important;
}

.o_calendar_view .fc-event {
    border-radius: 6px !important;
    border: none !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    transition: all 0.3s ease !important;
}

.o_calendar_view .fc-event:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Enhanced Pivot View (.o_pivot_view) */
.o_pivot_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    overflow: hidden !important;
    margin: 20px !important;
}

.o_pivot_view table {
    border-radius: 12px !important;
    overflow: hidden !important;
}

.o_pivot_view table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    border: none !important;
    padding: 16px 12px !important;
    text-align: center !important;
}

.o_pivot_view table tbody td {
    padding: 12px !important;
    border: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_pivot_view table tbody td:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

/* Enhanced Graph View (.o_graph_view) */
.o_graph_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    padding: 20px !important;
    margin: 20px !important;
}

.o_graph_view .o_graph_canvas_container {
    border-radius: 8px !important;
    overflow: hidden !important;
}

/* Enhanced Search View (.o_searchview) */
.o_searchview {
    background: white !important;
    border-radius: 25px !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease !important;
    overflow: hidden !important;
}

.o_searchview:focus-within {
    border-color: #667eea !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important;
    transform: translateY(-1px) !important;
}

.o_searchview .o_searchview_input {
    border: none !important;
    padding: 12px 20px !important;
    font-size: 14px !important;
    background: transparent !important;
}

.o_searchview .o_searchview_input::placeholder {
    color: #6c757d !important;
    font-style: italic !important;
}

/* Enhanced Activity View (.o_activity_view) */
.o_activity_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_activity_view .o_activity_record {
    border-radius: 8px !important;
    margin: 10px !important;
    padding: 15px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    border-left: 4px solid #667eea !important;
    transition: all 0.3s ease !important;
}

.o_activity_view .o_activity_record:hover {
    background: rgba(102, 126, 234, 0.05) !important;
    transform: translateX(5px) !important;
}

/* Enhanced Gantt View (.o_gantt_view) */
.o_gantt_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_gantt_view .o_gantt_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 15px !important;
    font-weight: 600 !important;
}

.o_gantt_view .o_gantt_row {
    border-bottom: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_gantt_view .o_gantt_row:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

/* Enhanced Map View (.o_map_view) */
.o_map_view {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
}

.o_map_view .o_map_container {
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Enhanced Dashboard View (.o_dashboard) */
.o_dashboard {
    background: transparent !important;
    padding: 20px !important;
}

.o_dashboard .o_dashboard_action {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin-bottom: 20px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.o_dashboard .o_dashboard_action:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12) !important;
}

.o_dashboard .o_dashboard_action .o_dashboard_action_title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 15px 20px !important;
    font-weight: 600 !important;
    font-size: 16px !important;
}
/* ========================================
   ADVANCED XML VIEWS ENHANCEMENTS
   ======================================== */

/* Enhanced Cohort View (.o_cohort_view) */
.o_cohort_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_cohort_view table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 15px 10px !important;
    border: none !important;
    text-align: center !important;
}

.o_cohort_view table tbody td {
    padding: 12px 8px !important;
    text-align: center !important;
    border: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_cohort_view table tbody td:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    transform: scale(1.05) !important;
}

/* Enhanced Timeline View (.o_timeline_view) */
.o_timeline_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_timeline_view .o_timeline_header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    padding: 20px !important;
    font-weight: 600 !important;
    font-size: 18px !important;
}

.o_timeline_view .vis-item {
    border-radius: 8px !important;
    border: none !important;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.o_timeline_view .vis-item:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

/* Enhanced Hierarchy View (.o_hierarchy_view) */
.o_hierarchy_view {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    padding: 20px !important;
}

.o_hierarchy_view .o_hierarchy_node {
    background: rgba(248, 249, 250, 0.8) !important;
    border: 2px solid rgba(102, 126, 234, 0.2) !important;
    border-radius: 12px !important;
    padding: 15px !important;
    margin: 10px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.o_hierarchy_view .o_hierarchy_node::before {
    content: '' !important;
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    height: 4px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-radius: 12px 12px 0 0 !important;
}

.o_hierarchy_view .o_hierarchy_node:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
    border-color: #667eea !important;
}

/* Enhanced Studio View (.o_web_studio_view) */
.o_web_studio_view {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.o_web_studio_view .o_web_studio_sidebar {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
}

.o_web_studio_view .o_web_studio_sidebar .o_web_studio_component {
    border-radius: 8px !important;
    margin: 8px !important;
    padding: 12px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    border: 1px solid rgba(102, 126, 234, 0.1) !important;
    transition: all 0.3s ease !important;
}

.o_web_studio_view .o_web_studio_sidebar .o_web_studio_component:hover {
    background: rgba(102, 126, 234, 0.05) !important;
    border-color: #667eea !important;
    transform: translateX(5px) !important;
}

/* Enhanced Spreadsheet View (.o_spreadsheet) */
.o_spreadsheet {
    background: white !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    margin: 20px !important;
    overflow: hidden !important;
}

.o_spreadsheet .o-grid {
    border-radius: 8px !important;
}

.o_spreadsheet .o-grid .o-grid-cell {
    border: 1px solid #f1f3f4 !important;
    transition: all 0.3s ease !important;
}

.o_spreadsheet .o-grid .o-grid-cell:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

.o_spreadsheet .o-grid .o-grid-cell.o-selected {
    border: 2px solid #667eea !important;
    background: rgba(102, 126, 234, 0.1) !important;
}

/* Enhanced Notebook/Tabs (.nav-tabs) */
.nav-tabs {
    border-bottom: 2px solid #e9ecef !important;
    margin-bottom: 20px !important;
}

.nav-tabs .nav-link {
    border: none !important;
    border-radius: 8px 8px 0 0 !important;
    padding: 12px 20px !important;
    margin-right: 5px !important;
    background: rgba(248, 249, 250, 0.5) !important;
    color: #6c757d !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.nav-tabs .nav-link:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
    transform: translateY(-2px) !important;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-bottom: 2px solid transparent !important;
}

.nav-tabs .nav-link.active::after {
    content: '' !important;
    position: absolute !important;
    bottom: -2px !important;
    left: 0 !important;
    right: 0 !important;
    height: 2px !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

/* Enhanced Statusbar (.o_statusbar_status) */
.o_statusbar_status {
    border-radius: 20px !important;
    margin: 0 4px !important;
    padding: 8px 16px !important;
    transition: all 0.3s ease !important;
    border: 2px solid transparent !important;
    background: rgba(248, 249, 250, 0.8) !important;
    color: #6c757d !important;
}

.o_statusbar_status:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
    transform: translateY(-1px) !important;
}

.o_statusbar_status.o_arrow_button_current {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    border-color: #667eea !important;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3) !important;
}

.o_statusbar_status.o_arrow_button_done {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white !important;
}

/* Enhanced One2many/Many2many Lists */
.o_field_one2many .o_list_view,
.o_field_many2many .o_list_view {
    border-radius: 8px !important;
    border: 2px solid rgba(102, 126, 234, 0.1) !important;
    overflow: hidden !important;
}

.o_field_one2many .o_list_view .o_list_table thead th,
.o_field_many2many .o_list_view .o_list_table thead th {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%) !important;
    color: white !important;
    font-size: 13px !important;
    padding: 12px 8px !important;
}

/* Enhanced Selection Fields */
.o_field_widget.o_field_selection select,
.o_field_widget.o_field_many2one input {
    background: white !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_selection select:focus,
.o_field_widget.o_field_many2one input:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    transform: translateY(-1px) !important;
}

/* Enhanced Boolean Fields */
.o_field_widget.o_field_boolean input[type="checkbox"] {
    width: 20px !important;
    height: 20px !important;
    border-radius: 4px !important;
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_boolean input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    border-color: #667eea !important;
}

/* Enhanced Date/DateTime Fields */
.o_field_widget.o_field_date input,
.o_field_widget.o_field_datetime input {
    background: white !important;
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    transition: all 0.3s ease !important;
    position: relative !important;
}

.o_field_widget.o_field_date input:focus,
.o_field_widget.o_field_datetime input:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Enhanced Text/HTML Fields */
.o_field_widget.o_field_text textarea,
.o_field_widget.o_field_html .note-editor {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_text textarea:focus,
.o_field_widget.o_field_html .note-editor.note-frame.codeview {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
}

/* Enhanced Binary/Image Fields */
.o_field_widget.o_field_binary,
.o_field_widget.o_field_image {
    border-radius: 8px !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_field_binary:hover,
.o_field_widget.o_field_image:hover {
    transform: scale(1.02) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important;
}

/* Enhanced Monetary Fields */
.o_field_widget.o_field_monetary input {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.05) 0%, rgba(32, 201, 151, 0.05) 100%) !important;
    border: 2px solid rgba(40, 167, 69, 0.2) !important;
    border-radius: 8px !important;
    padding: 10px 12px !important;
    font-weight: 600 !important;
    color: #28a745 !important;
}

.o_field_widget.o_field_monetary input:focus {
    border-color: #28a745 !important;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
}

/* Enhanced Progress Bar Fields */
.o_field_widget.o_field_progressbar .progress {
    height: 12px !important;
    border-radius: 6px !important;
    background: #e9ecef !important;
    overflow: hidden !important;
}

.o_field_widget.o_field_progressbar .progress-bar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    transition: all 0.3s ease !important;
}

/* Enhanced Priority/Stars Fields */
.o_field_widget.o_priority .o_priority_star {
    color: #ffc107 !important;
    font-size: 18px !important;
    transition: all 0.3s ease !important;
}

.o_field_widget.o_priority .o_priority_star:hover {
    transform: scale(1.2) !important;
    text-shadow: 0 2px 4px rgba(255, 193, 7, 0.3) !important;
}

/* ========================================
   ENHANCED RESPONSIVE DESIGN SYSTEM
   ======================================== */

/* Responsive Container System */
.lindo-container {
    width: 100%;
    padding-left: 16px;
    padding-right: 16px;
    margin-left: auto;
    margin-right: auto;
}

.lindo-container-fluid {
    width: 100%;
    padding-left: 16px;
    padding-right: 16px;
}

/* Responsive Grid System */
.lindo-row {
    display: flex;
    flex-wrap: wrap;
    margin-left: -8px;
    margin-right: -8px;
}

.lindo-col {
    flex: 1 0 0%;
    padding-left: 8px;
    padding-right: 8px;
}

/* Column Sizes */
.lindo-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.lindo-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.lindo-col-3 { flex: 0 0 25%; max-width: 25%; }
.lindo-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.lindo-col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.lindo-col-6 { flex: 0 0 50%; max-width: 50%; }
.lindo-col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.lindo-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.lindo-col-9 { flex: 0 0 75%; max-width: 75%; }
.lindo-col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.lindo-col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.lindo-col-12 { flex: 0 0 100%; max-width: 100%; }

/* Responsive Utilities */
.lindo-d-none { display: none !important; }
.lindo-d-block { display: block !important; }
.lindo-d-flex { display: flex !important; }
.lindo-d-inline { display: inline !important; }
.lindo-d-inline-block { display: inline-block !important; }

/* Text Alignment */
.lindo-text-left { text-align: left !important; }
.lindo-text-center { text-align: center !important; }
.lindo-text-right { text-align: right !important; }

/* Spacing Utilities */
.lindo-m-0 { margin: 0 !important; }
.lindo-m-1 { margin: 4px !important; }
.lindo-m-2 { margin: 8px !important; }
.lindo-m-3 { margin: 16px !important; }
.lindo-m-4 { margin: 24px !important; }
.lindo-m-5 { margin: 32px !important; }

.lindo-p-0 { padding: 0 !important; }
.lindo-p-1 { padding: 4px !important; }
.lindo-p-2 { padding: 8px !important; }
.lindo-p-3 { padding: 16px !important; }
.lindo-p-4 { padding: 24px !important; }
.lindo-p-5 { padding: 32px !important; }

/* ========================================
   MOBILE-FIRST RESPONSIVE BREAKPOINTS
   ======================================== */

/* Extra Small Devices (Portrait Phones) - 575px and down */
@media (max-width: 575.98px) {
    .lindo-container {
        max-width: 100%;
        padding-left: 8px;
        padding-right: 8px;
    }

    /* Mobile Navigation */
    .o_main_navbar {
        padding: 4px 8px !important;
        flex-wrap: wrap !important;
        min-height: 50px !important;
    }

    .o_main_navbar .o_menu_brand {
        font-size: 14px !important;
    }

    .o_main_navbar .o_menu_sections {
        display: none !important;
    }

    .o_main_navbar .o_menu_systray {
        margin-left: auto !important;
    }

    .o_main_navbar .o_menu_systray .o_nav_entry,
    .o_main_navbar .o_menu_systray .dropdown-toggle {
        width: 36px !important;
        height: 36px !important;
        font-size: 14px !important;
    }

    /* Mobile Action Manager */
    .o_action_manager > .o_view_controller {
        margin: 4px !important;
        border-radius: 6px !important;
    }

    /* Mobile Forms */
    .o_form_sheet {
        margin: 4px !important;
        padding: 8px !important;
    }

    .o_form_view .o_group {
        padding: 8px !important;
        margin-bottom: 12px !important;
    }

    .o_form_view .o_group .o_td_label {
        padding: 8px 0 !important;
        font-size: 13px !important;
    }

    /* Mobile Tables */
    .o_list_table {
        font-size: 11px !important;
    }

    .o_list_table thead th,
    .o_list_table tbody td {
        padding: 4px 6px !important;
        font-size: 11px !important;
    }

    .o_list_table thead th {
        font-size: 10px !important;
        letter-spacing: 0.3px !important;
    }

    /* Mobile Kanban */
    .o_kanban_view {
        padding: 8px !important;
    }

    .o_kanban_record {
        margin-bottom: 8px !important;
        padding: 8px !important;
    }

    /* Mobile Form Controls */
    .o_field_widget input,
    .o_field_widget select,
    .o_field_widget textarea {
        padding: 8px !important;
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important; /* Touch target size */
    }

    /* Mobile Buttons */
    .btn {
        padding: 8px 12px !important;
        font-size: 14px !important;
        min-height: 44px !important; /* Touch target size */
    }

    /* Mobile Grid */
    .lindo-col-xs-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .lindo-col-xs-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .lindo-col-xs-3 { flex: 0 0 25%; max-width: 25%; }
    .lindo-col-xs-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .lindo-col-xs-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .lindo-col-xs-6 { flex: 0 0 50%; max-width: 50%; }
    .lindo-col-xs-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .lindo-col-xs-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .lindo-col-xs-9 { flex: 0 0 75%; max-width: 75%; }
    .lindo-col-xs-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .lindo-col-xs-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .lindo-col-xs-12 { flex: 0 0 100%; max-width: 100%; }

    /* Mobile Display Utilities */
    .lindo-d-xs-none { display: none !important; }
    .lindo-d-xs-block { display: block !important; }
    .lindo-d-xs-flex { display: flex !important; }

    /* Mobile Text Alignment */
    .lindo-text-xs-left { text-align: left !important; }
    .lindo-text-xs-center { text-align: center !important; }
    .lindo-text-xs-right { text-align: right !important; }
}

/* Small Devices (Landscape Phones) - 576px to 767px */
@media (min-width: 576px) and (max-width: 767.98px) {
    .lindo-container {
        max-width: 540px;
    }

    /* Small Device Grid */
    .lindo-col-sm-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .lindo-col-sm-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .lindo-col-sm-3 { flex: 0 0 25%; max-width: 25%; }
    .lindo-col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .lindo-col-sm-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .lindo-col-sm-6 { flex: 0 0 50%; max-width: 50%; }
    .lindo-col-sm-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .lindo-col-sm-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .lindo-col-sm-9 { flex: 0 0 75%; max-width: 75%; }
    .lindo-col-sm-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .lindo-col-sm-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .lindo-col-sm-12 { flex: 0 0 100%; max-width: 100%; }

    /* Small Device Display Utilities */
    .lindo-d-sm-none { display: none !important; }
    .lindo-d-sm-block { display: block !important; }
    .lindo-d-sm-flex { display: flex !important; }

    /* Small Device Text Alignment */
    .lindo-text-sm-left { text-align: left !important; }
    .lindo-text-sm-center { text-align: center !important; }
    .lindo-text-sm-right { text-align: right !important; }

    /* Small Device Odoo Adjustments */
    .o_main_navbar .o_menu_sections {
        display: flex !important;
    }

    .o_action_manager > .o_view_controller {
        margin: 8px !important;
    }

    .o_form_sheet {
        margin: 8px !important;
        padding: 12px !important;
    }
}

/* Medium Devices (Tablets) - 768px to 991px */
@media (min-width: 768px) and (max-width: 991.98px) {
    .lindo-container {
        max-width: 720px;
    }

    /* Medium Device Grid */
    .lindo-col-md-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .lindo-col-md-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .lindo-col-md-3 { flex: 0 0 25%; max-width: 25%; }
    .lindo-col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .lindo-col-md-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .lindo-col-md-6 { flex: 0 0 50%; max-width: 50%; }
    .lindo-col-md-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .lindo-col-md-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .lindo-col-md-9 { flex: 0 0 75%; max-width: 75%; }
    .lindo-col-md-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .lindo-col-md-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .lindo-col-md-12 { flex: 0 0 100%; max-width: 100%; }

    /* Medium Device Display Utilities */
    .lindo-d-md-none { display: none !important; }
    .lindo-d-md-block { display: block !important; }
    .lindo-d-md-flex { display: flex !important; }

    /* Medium Device Text Alignment */
    .lindo-text-md-left { text-align: left !important; }
    .lindo-text-md-center { text-align: center !important; }
    .lindo-text-md-right { text-align: right !important; }

    /* Tablet Odoo Adjustments */
    .o_action_manager > .o_view_controller {
        margin: 12px !important;
    }

    .o_form_sheet {
        margin: 12px !important;
        padding: 16px !important;
    }

    .o_list_table thead th,
    .o_list_table tbody td {
        padding: 8px 10px !important;
        font-size: 13px !important;
    }
}

/* Large Devices (Desktops) - 992px to 1199px */
@media (min-width: 992px) and (max-width: 1199.98px) {
    .lindo-container {
        max-width: 960px;
    }

    /* Large Device Grid */
    .lindo-col-lg-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .lindo-col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .lindo-col-lg-3 { flex: 0 0 25%; max-width: 25%; }
    .lindo-col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .lindo-col-lg-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .lindo-col-lg-6 { flex: 0 0 50%; max-width: 50%; }
    .lindo-col-lg-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .lindo-col-lg-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .lindo-col-lg-9 { flex: 0 0 75%; max-width: 75%; }
    .lindo-col-lg-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .lindo-col-lg-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .lindo-col-lg-12 { flex: 0 0 100%; max-width: 100%; }

    /* Large Device Display Utilities */
    .lindo-d-lg-none { display: none !important; }
    .lindo-d-lg-block { display: block !important; }
    .lindo-d-lg-flex { display: flex !important; }

    /* Large Device Text Alignment */
    .lindo-text-lg-left { text-align: left !important; }
    .lindo-text-lg-center { text-align: center !important; }
    .lindo-text-lg-right { text-align: right !important; }
}

/* Extra Large Devices (Large Desktops) - 1200px and up */
@media (min-width: 1200px) {
    .lindo-container {
        max-width: 1140px;
    }

    /* Extra Large Device Grid */
    .lindo-col-xl-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
    .lindo-col-xl-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
    .lindo-col-xl-3 { flex: 0 0 25%; max-width: 25%; }
    .lindo-col-xl-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
    .lindo-col-xl-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
    .lindo-col-xl-6 { flex: 0 0 50%; max-width: 50%; }
    .lindo-col-xl-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
    .lindo-col-xl-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
    .lindo-col-xl-9 { flex: 0 0 75%; max-width: 75%; }
    .lindo-col-xl-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
    .lindo-col-xl-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
    .lindo-col-xl-12 { flex: 0 0 100%; max-width: 100%; }

    /* Extra Large Device Display Utilities */
    .lindo-d-xl-none { display: none !important; }
    .lindo-d-xl-block { display: block !important; }
    .lindo-d-xl-flex { display: flex !important; }

    /* Extra Large Device Text Alignment */
    .lindo-text-xl-left { text-align: left !important; }
    .lindo-text-xl-center { text-align: center !important; }
    .lindo-text-xl-right { text-align: right !important; }
}

/* ========================================
   MOBILE TOUCH & INTERACTION ENHANCEMENTS
   ======================================== */

/* Touch Active States */
.lindo-touch-active {
    background: rgba(102, 126, 234, 0.1) !important;
    transform: scale(0.98) !important;
    transition: all 0.1s ease !important;
}

/* Mobile Mode Specific Styles */
.lindo-mobile-mode .o_list_table tbody tr:hover {
    transform: none !important; /* Disable hover transforms on mobile */
}

.lindo-mobile-mode .o_kanban_record:hover {
    transform: none !important; /* Disable hover transforms on mobile */
}

/* Mobile Navigation Toggle */
.lindo-mobile-toggle {
    background: rgba(255, 255, 255, 0.1) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 36px !important;
    height: 36px !important;
    color: white !important;
    font-size: 16px !important;
    cursor: pointer !important;
    margin-left: 8px !important;
    transition: all 0.3s ease !important;
}

.lindo-mobile-toggle:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) !important;
}

/* Mobile Menu Styles */
.o_menu_sections.lindo-mobile-open {
    position: fixed !important;
    top: 60px !important;
    left: 0 !important;
    right: 0 !important;
    background: white !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    z-index: 1000 !important;
    padding: 16px !important;
    border-radius: 0 0 12px 12px !important;
}

.o_menu_sections.lindo-mobile-open .o_nav_entry {
    display: block !important;
    width: 100% !important;
    padding: 12px 16px !important;
    margin: 4px 0 !important;
    border-radius: 8px !important;
    color: #495057 !important;
    background: rgba(248, 249, 250, 0.5) !important;
    border: 1px solid rgba(102, 126, 234, 0.1) !important;
}

.o_menu_sections.lindo-mobile-open .o_nav_entry:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
}

/* Show mobile toggle only on small screens */
@media (max-width: 767.98px) {
    .lindo-mobile-toggle {
        display: block !important;
    }
}

@media (min-width: 768px) {
    .lindo-mobile-toggle {
        display: none !important;
    }

    .o_menu_sections.lindo-mobile-open {
        position: static !important;
        background: transparent !important;
        box-shadow: none !important;
        padding: 0 !important;
        border-radius: 0 !important;
    }
}

/* ========================================
   ENHANCED ACCESSIBILITY & FOCUS STATES
   ======================================== */

/* Enhanced Focus Indicators */
*:focus-visible {
    outline: 2px solid #667eea !important;
    outline-offset: 2px !important;
    border-radius: 4px !important;
}

/* Skip to Content Link */
.lindo-skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: #667eea;
    color: white;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
    transition: top 0.3s ease;
}

.lindo-skip-link:focus {
    top: 6px;
}

/* High Contrast Mode Enhancements */
@media (prefers-contrast: high) {
    .o_list_table tbody tr:hover,
    .o_kanban_record:hover,
    .o_field_widget:focus-within {
        outline: 3px solid #0066cc !important;
        outline-offset: 2px !important;
    }

    .btn-primary {
        background: #0066cc !important;
        border: 2px solid #004499 !important;
    }

    .o_main_navbar {
        background: #0066cc !important;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }

    .o_list_table tbody tr:hover,
    .o_kanban_record:hover {
        transform: none !important;
    }
}

/* ========================================
   PRINT OPTIMIZATIONS
   ======================================== */

@media print {
    /* Hide interactive elements */
    .o_main_navbar,
    .o_search_panel,
    .lindo-mobile-toggle,
    .o_menu_systray,
    .btn,
    .o_statusbar_status {
        display: none !important;
    }

    /* Optimize layout for printing */
    .o_action_manager > .o_view_controller {
        margin: 0 !important;
        box-shadow: none !important;
        border-radius: 0 !important;
        border: 1px solid #000 !important;
    }

    .o_list_table,
    .o_form_sheet {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        background: white !important;
    }

    .o_list_table thead th {
        background: #f0f0f0 !important;
        color: #000 !important;
        border: 1px solid #000 !important;
    }

    .o_list_table tbody td {
        border: 1px solid #ccc !important;
        color: #000 !important;
    }

    /* Ensure text is readable */
    * {
        color: #000 !important;
        background: transparent !important;
    }

    /* Page breaks */
    .o_form_sheet,
    .o_list_view,
    .o_kanban_view {
        page-break-inside: avoid;
    }
}

/* ========================================
   PERFORMANCE OPTIMIZATIONS
   ======================================== */

/* GPU Acceleration for Smooth Animations */
.o_list_table tbody tr,
.o_kanban_record,
.btn,
.o_field_widget {
    will-change: transform;
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Optimize Repaints */
.o_main_navbar,
.o_action_manager > .o_view_controller {
    contain: layout style paint;
}

/* Lazy Loading Placeholder */
.lindo-lazy-placeholder {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: lindo-loading-shimmer 1.5s infinite;
}

@keyframes lindo-loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* ========================================
   BROWSER-SPECIFIC OPTIMIZATIONS
   ======================================== */

/* Safari Specific */
@supports (-webkit-appearance: none) {
    .o_field_widget input,
    .o_field_widget select,
    .o_field_widget textarea {
        -webkit-appearance: none;
        border-radius: 8px;
    }
}

/* Firefox Specific */
@-moz-document url-prefix() {
    .o_list_table {
        scrollbar-width: thin;
        scrollbar-color: #667eea #f1f1f1;
    }
}

/* Edge/IE Specific */
@supports (-ms-ime-align: auto) {
    .o_main_navbar {
        background: #667eea; /* Fallback for gradient */
    }
}

/* ========================================
   DARK MODE ENHANCEMENTS
   ======================================== */

@media (prefers-color-scheme: dark) {
    :root {
        --lindo-white: #1a1a1a;
        --lindo-light: #2d2d2d;
        --lindo-gray-100: #3a3a3a;
        --lindo-gray-200: #4a4a4a;
        --lindo-gray-300: #5a5a5a;
        --lindo-gray-400: #6a6a6a;
        --lindo-gray-500: #7a7a7a;
        --lindo-gray-600: #8a8a8a;
        --lindo-gray-700: #9a9a9a;
        --lindo-gray-800: #aaaaaa;
        --lindo-gray-900: #ffffff;
    }

    body {
        background: #1a1a1a;
        color: #ffffff;
    }

    .o_web_client {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%) !important;
    }

    .o_action_manager > .o_view_controller,
    .o_form_sheet,
    .o_list_view,
    .o_kanban_record {
        background: #2d2d2d !important;
        color: #ffffff !important;
        border-color: #4a4a4a !important;
    }
}